<template>
  <div class="h-full p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">群发消息</h1>
        <p class="mt-2 text-gray-600">向企业微信群发送消息</p>
      </div>

      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- 左侧：群列表 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">选择群聊</h2>
            <Button
              type="primary"
              :loading="loadingGroups"
              @click="loadChatGroups"
              :icon="h(ReloadOutlined)"
            >
              刷新群列表
            </Button>
          </div>

          <!-- 搜索框 -->
          <div v-if="chatGroups.length > 0">
            <Input
              v-model:value="searchKeyword"
              placeholder="搜索群聊名称或群主..."
              allow-clear
              class="mb-3"
            />
          </div>

          <!-- 全选操作 -->
          <div v-if="filteredChatGroups.length > 0" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-2">
              <Checkbox
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="toggleSelectAll"
              />
              <span class="text-sm text-gray-700">全选</span>
            </div>
            <div class="text-sm text-gray-500">
              {{ searchKeyword ? `找到 ${filteredChatGroups.length} 个群聊` : `共 ${chatGroups.length} 个群聊` }}
            </div>
          </div>

          <!-- 群列表加载状态 -->
          <div v-if="loadingGroups" class="flex justify-center py-8">
            <Spin size="large" />
          </div>

          <!-- 群列表 -->
          <div v-else-if="filteredChatGroups.length > 0" class="space-y-1 max-h-96 overflow-y-auto">
            <div
              v-for="group in filteredChatGroups"
              :key="group.chatId"
              class="flex items-center space-x-3 px-3 py-2 border border-gray-200 rounded hover:bg-gray-50 cursor-pointer group-item"
              :class="{ 'bg-blue-50 border-blue-300': selectedGroups.has(group.chatId) }"
              @click="toggleGroupSelection(group)"
            >
              <Checkbox
                :checked="selectedGroups.has(group.chatId)"
                @change="toggleGroupSelection(group)"
              />
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 truncate">
                  {{ group.chatName || '未命名群聊' }}
                </div>
                <div class="text-xs text-gray-500">
                  群主：{{ group.ownerName }}
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索无结果 -->
          <div v-else-if="chatGroups.length > 0 && searchKeyword.trim()" class="text-center py-8">
            <div class="text-gray-500 mb-2">未找到匹配的群聊</div>
            <Button type="link" @click="searchKeyword = ''">清除搜索</Button>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-8">
            <div class="text-gray-500 mb-2">暂无群聊数据</div>
            <Button type="link" @click="loadChatGroups" :loading="loadingGroups">
              {{ loadingGroups ? '加载中...' : '点击加载' }}
            </Button>
          </div>

          <!-- 选择统计 -->
          <div v-if="selectedGroups.size > 0" class="text-sm text-blue-600">
            已选择 {{ selectedGroups.size }} 个群聊
          </div>
        </div>

        <!-- 右侧：消息编辑 -->
        <div class="space-y-4">
          <h2 class="text-lg font-medium text-gray-900">编辑消息</h2>
          
          <!-- 消息输入框 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              消息内容
            </label>
            <Textarea
              v-model:value="messageContent"
              placeholder="请输入要发送的消息内容..."
              :rows="8"
              :maxlength="1000"
              show-count
              class="w-full"
            />
          </div>

          <!-- 发送按钮 -->
          <div class="flex justify-end space-x-3">
            <Button @click="clearAll">清空</Button>
            <Button 
              type="primary" 
              :loading="sendingMessage"
              :disabled="!canSendMessage"
              @click="sendMessage"
            >
              发送消息
            </Button>
          </div>

          <!-- 发送预览 -->
          <div v-if="selectedGroups.size > 0 && messageContent.trim()" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 class="text-sm font-medium text-gray-900 mb-2">发送预览</h3>
            <div class="text-sm text-gray-600 mb-2">
              将向 {{ selectedGroups.size }} 个群聊发送以下消息：
            </div>
            <div class="text-sm bg-white p-3 rounded border">
              {{ messageContent }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref, computed, onMounted } from 'vue';
import { Button, Checkbox, Input, Spin, Textarea, message } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import type { ChatGroupInfo } from '#/api/jsj-ai/types';
import { fetchChatGroupList, sendGroupMessage } from '#/api/jsj-ai/api-v2';

// 响应式数据
const chatGroups = ref<ChatGroupInfo[]>([]);
const selectedGroups = ref<Set<string>>(new Set());
const messageContent = ref('');
const searchKeyword = ref('');
const loadingGroups = ref(false);
const sendingMessage = ref(false);

// 计算属性
const canSendMessage = computed(() => {
  return selectedGroups.value.size > 0 && messageContent.value.trim().length > 0 && !sendingMessage.value;
});

// 获取选中的群聊信息
const getSelectedGroupsInfo = computed(() => {
  return chatGroups.value.filter(group => selectedGroups.value.has(group.chatId));
});

// 全选状态
const isAllSelected = computed(() => {
  return filteredChatGroups.value.length > 0 &&
         filteredChatGroups.value.every(group => selectedGroups.value.has(group.chatId));
});

// 半选状态
const isIndeterminate = computed(() => {
  return selectedGroups.value.size > 0 && selectedGroups.value.size < filteredChatGroups.value.length;
});

// 过滤后的群聊列表
const filteredChatGroups = computed(() => {
  if (!searchKeyword.value.trim()) {
    return chatGroups.value;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return chatGroups.value.filter(group =>
    group.chatName.toLowerCase().includes(keyword) ||
    group.ownerName.toLowerCase().includes(keyword)
  );
});

// 加载群聊列表
const loadChatGroups = async () => {
  loadingGroups.value = true;
  try {
    const groups = await fetchChatGroupList();
    chatGroups.value = groups;
    message.success(`成功加载 ${groups.length} 个群聊`);
  } catch (error: any) {
    console.error('加载群聊列表失败:', error);
    message.error(error.message || '加载群聊列表失败');
  } finally {
    loadingGroups.value = false;
  }
};

// 切换群聊选择状态
const toggleGroupSelection = (group: ChatGroupInfo) => {
  const newSelected = new Set(selectedGroups.value);
  if (newSelected.has(group.chatId)) {
    newSelected.delete(group.chatId);
  } else {
    newSelected.add(group.chatId);
  }
  selectedGroups.value = newSelected;
};

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 取消选择当前过滤结果中的所有群聊
    filteredChatGroups.value.forEach(group => {
      selectedGroups.value.delete(group.chatId);
    });
  } else {
    // 选择当前过滤结果中的所有群聊
    filteredChatGroups.value.forEach(group => {
      selectedGroups.value.add(group.chatId);
    });
  }
};

// 发送消息
const sendMessage = async () => {
  if (!canSendMessage.value) return;

  sendingMessage.value = true;
  try {
    const selectedGroupsInfo = getSelectedGroupsInfo.value;
    const result = await sendGroupMessage({
      chatIdList: selectedGroupsInfo,
      msg: messageContent.value.trim(),
    });

    message.success(result || '消息发送成功');
    
    // 发送成功后清空内容
    clearAll();
  } catch (error: any) {
    console.error('发送消息失败:', error);
    message.error(error.message || '发送消息失败');
  } finally {
    sendingMessage.value = false;
  }
};

// 清空所有内容
const clearAll = () => {
  selectedGroups.value.clear();
  messageContent.value = '';
};

// 页面加载时获取群聊列表
onMounted(() => {
  loadChatGroups();
});
</script>

<style scoped>
/* 自定义样式 */
.ant-checkbox-wrapper {
  margin-right: 0;
}

/* 群聊列表项高度控制 */
.group-item {
  height: auto;
  min-height: 30px;
}

/* 滚动条样式 */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
