import type {
  AccountSubjectQueryParams,
  AccountSubjectResponseData,
  AIAccountSubjectResponseData,
  AssistantAccountingQueryParams,
  AssistantAccountingResponseData,
  BankReceiptData,
  BankReceiptQueryParams,
  BankReceiptUpdateSceneParams,
  BatchUpdateResponse,
  ChangeDataSourceParams,
  ChangeDataSourceResponse,
  CompaniesQueryParams,
  CompanyData,
  InvoiceData,
  InvoiceQueryParams,
  InvoiceUpdateSceneParams,
  PayrollData,
  PayrollQueryParams,
  ScenarioConditionAddParams,
  ScenarioConditionAddResponse,
  ScenarioConditionConfigData,
  ScenarioConditionConfigQueryParams,
  ScenarioConditionData,
  ScenarioConditionDeleteParams,
  ScenarioConditionDeleteResponse,
  ScenarioConditionQueryParams,
  ScenarioConditionUpdateParams,
  ScenarioConditionUpdateResponse,
  ScenarioEntryAddParams,
  ScenarioEntryAddResponse,
  ScenarioEntryData,
  ScenarioEntryDeleteParams,
  ScenarioEntryDeleteResponse,
  ScenarioEntryQueryParams,
  ScenarioEntryUpdateParams,
  ScenarioEntryUpdateResponse,
  SubjectMappingUpdateParams,
  SubjectMappingUpdateResponse,
  SyncAccountSubjectsParams,
  SyncAccountSubjectsResponse,
  UpdateAutoModeParams,
  UpdateAutoModeResponse,
  VoucherPdfGenerateParams,
  VoucherPdfGenerateResponse,
  VoucherQueryParams,
  VoucherResponseData,
  VoucherSourceDataQueryParams,
  VoucherSourceDataResponse,
  UpdateVoucherParams,
  UpdateVoucherResponse,
  UpdateVoucherDetailParams,
  UpdateVoucherDetailResponse,
  MergeVouchersParams,
  MergeVouchersResponse,
  GeneralMergeVouchersParams,
  GeneralMergeVouchersResponse,
  WriteBackVouchersParams,
  WriteBackVouchersResponse,
  UserCustomerNamesQueryParams,
  UserCustomerNamesResponseData,
  DeleteVouchersParams,
  DeleteVouchersResponse,
  SyncCompaniesParams,
  SyncCompaniesResponse,
  ThirdPartyAuthParams,
  ThirdPartyAuthResponse,
  UpdatePlatformUserInfoParams,
  UpdatePlatformUserInfoResponse,
  ChatGroupInfo,
  ChatGroupListResponse,
  SendGroupMessageParams,
  SendGroupMessageResponse,
} from './types';

import { httpClient, apiCall, type ApiResponse } from './request';
import { requestClient } from '#/api/request';

// ==================== 查询类API ====================

/**
 * 查询发票列表
 */
export async function fetchInvoiceList(params: InvoiceQueryParams): Promise<InvoiceData[]> {
  return apiCall(() => httpClient.get<ApiResponse<InvoiceData[]>>('/invoice/list', { params }));
}

/**
 * 查询银行回单列表
 */
export async function fetchBankReceiptList(params: BankReceiptQueryParams): Promise<BankReceiptData[]> {
  return apiCall(() => httpClient.get<ApiResponse<BankReceiptData[]>>('/bank_receipts/list', { params }));
}

/**
 * 查询工资单列表
 */
export async function fetchPayrollList(params: PayrollQueryParams): Promise<PayrollData[]> {
  return apiCall(() => httpClient.get<ApiResponse<PayrollData[]>>('/payroll/list', { params }));
}

/**
 * 查询场景分录列表
 */
export async function fetchScenarioEntryList(params: ScenarioEntryQueryParams): Promise<ScenarioEntryData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioEntryData[]>>('/scenario_entry/list', { params }));
}

/**
 * 更新场景条目
 */
export async function updateScenarioEntry(params: ScenarioEntryUpdateParams): Promise<ScenarioEntryUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<ScenarioEntryUpdateResponse>>('/scenario_entry/update', params));
}

/**
 * 查询场景条件列表
 */
export async function fetchScenarioConditionList(params: ScenarioConditionQueryParams): Promise<ScenarioConditionData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioConditionData[]>>('/scenario_condition/list', { params }));
}

/**
 * 添加场景条件
 */
export async function addScenarioCondition(params: ScenarioConditionAddParams): Promise<ScenarioConditionAddResponse> {
  return apiCall(() => httpClient.post<ApiResponse<ScenarioConditionAddResponse>>('/accounting_scenario_condition/add', params));
}

/**
 * 添加场景分录
 */
export async function addScenarioEntry(params: ScenarioEntryAddParams): Promise<ScenarioEntryAddResponse> {
  return apiCall(() => httpClient.post<ApiResponse<ScenarioEntryAddResponse>>('/scenario_entry/add', params));
}

/**
 * 批量更新发票场景
 */
export async function updateInvoiceScene(params: InvoiceUpdateSceneParams[]): Promise<BatchUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<BatchUpdateResponse>>('/invoice_service/update_scene', params));
}

/**
 * 批量更新银行流水场景
 */
export async function updateBankReceiptScene(params: BankReceiptUpdateSceneParams[]): Promise<BatchUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<BatchUpdateResponse>>('/bank_receipt/update_scene', params));
}

/**
 * 查询科目列表
 */
export async function fetchAccountSubjectList(params: AccountSubjectQueryParams): Promise<AccountSubjectResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<AccountSubjectResponseData>>('/account-subjects/list', { params }));
}

/**
 * 查询辅助核算列表
 */
export async function fetchAssistantAccountingList(params: AssistantAccountingQueryParams): Promise<AssistantAccountingResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<AssistantAccountingResponseData>>('/assistant-accounting/list', { params }));
}


/**
 * 删除场景条件
 */
export async function deleteScenarioCondition(params: ScenarioConditionDeleteParams): Promise<ScenarioConditionDeleteResponse> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioConditionDeleteResponse>>('/scenario_condition/delete', { params }));
}

/**
 * 更新场景条件
 */
export async function updateScenarioCondition(params: ScenarioConditionUpdateParams): Promise<ScenarioConditionUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<ScenarioConditionUpdateResponse>>('/scenario_condition/update', params));
}

/**
 * 删除场景分录
 */
export async function deleteScenarioEntry(params: ScenarioEntryDeleteParams): Promise<ScenarioEntryDeleteResponse> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioEntryDeleteResponse>>('/scenario_entry/delete', { params }));
}

/**
 * 查询公司列表
 */
export async function fetchCompaniesList(params: CompaniesQueryParams): Promise<CompanyData[]> {
  const response: any = await apiCall(() => httpClient.get<ApiResponse<{ companies: CompanyData[] }>>('/companies/list', { params }));
  return response.companies || [];
}

/**
 * 更新AI自动模式
 */
export async function updateAutoMode(params: UpdateAutoModeParams): Promise<UpdateAutoModeResponse> {
  return apiCall(() => httpClient.post<ApiResponse<UpdateAutoModeResponse>>('/companies/update-auto-mode', params));
}

/**
 * 生成凭证PDF
 */
export async function generateVoucherPdf(params: VoucherPdfGenerateParams): Promise<VoucherPdfGenerateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<VoucherPdfGenerateResponse>>('/voucher/generate_pdf', params));
}

/**
 * 获取当前公司某个月份的凭证信息
 * 移除重复的缓存机制，依赖httpClient的内置缓存
 */
export async function getCurrentVouchers(params: VoucherQueryParams): Promise<VoucherResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<VoucherResponseData>>('/vouchers/current', { params }));
}

/**
 * 获取凭证原始数据
 */
export async function getVoucherSourceData(params: VoucherSourceDataQueryParams): Promise<VoucherSourceDataResponse> {
  return apiCall(() => httpClient.get<ApiResponse<VoucherSourceDataResponse>>('/vouchers/source-data', { params }));
}

// ==================== 凭证操作类API ====================

/**
 * 修改凭证信息
 */
export async function updateVoucher(data: UpdateVoucherParams): Promise<UpdateVoucherResponse> {
  return apiCall(() => httpClient.put<ApiResponse<UpdateVoucherResponse>>('/vouchers/update', data));
}

/**
 * 更新凭证明细
 * (在银行回单列表中修改相关凭证科目使用)
 */
export async function updateVoucherDetailById(data: UpdateVoucherDetailParams): Promise<UpdateVoucherDetailResponse> {
  return apiCall(() => httpClient.post<ApiResponse<UpdateVoucherDetailResponse>>('/vouchers/update_detail_by_id', data));
}

/**
 * 合并凭证
 */
export async function mergeVouchers(data: MergeVouchersParams): Promise<MergeVouchersResponse> {
  return apiCall(() => httpClient.post<ApiResponse<MergeVouchersResponse>>('/vouchers/merge', data));
}

/**
 * 通用凭证合并
 */
export async function generalMergeVouchers(data: GeneralMergeVouchersParams): Promise<GeneralMergeVouchersResponse> {
  return apiCall(() => httpClient.post<ApiResponse<GeneralMergeVouchersResponse>>('/vouchers/general_merge', data));
}

/**
 * 凭证写入
 */
export async function writeBackVouchers(data: WriteBackVouchersParams): Promise<WriteBackVouchersResponse> {
  return apiCall(() => httpClient.post<ApiResponse<WriteBackVouchersResponse>>('/vouchers/write_back', data));
}

/**
 * 获取用户负责的客户名称列表
 */
export async function getUserCustomerNames(params: UserCustomerNamesQueryParams): Promise<UserCustomerNamesResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<UserCustomerNamesResponseData>>('/users/get_user_info', { params }));
}

/**
 * 查询场景条件配置
 */
export async function fetchScenarioConditionConfig(params: ScenarioConditionConfigQueryParams): Promise<ScenarioConditionConfigData[]> {
  return apiCall(() => httpClient.get<ApiResponse<ScenarioConditionConfigData[]>>('/config/get_by_type', { params }));
}

/**
 * 删除凭证
 */
export async function deleteVouchers(data: DeleteVouchersParams): Promise<DeleteVouchersResponse> {
  return apiCall(() => httpClient.delete<ApiResponse<DeleteVouchersResponse>>('/vouchers/delete', { data }));
}

/**
 * 同步公司信息
 */
export async function syncCompanies(params: SyncCompaniesParams): Promise<SyncCompaniesResponse> {
  return apiCall(() => httpClient.post<ApiResponse<SyncCompaniesResponse>>('/companies/sync', params));
}

/**
 * 切换数据源
 */
export async function changeDataSource(params: ChangeDataSourceParams): Promise<ChangeDataSourceResponse> {
  return apiCall(() => httpClient.post<ApiResponse<ChangeDataSourceResponse>>('/accounting-summary/change_data_source', params));
}

// ==================== 第三方认证API ====================

/**
 * 第三方认证接口
 * 通过第三方token获取用户名和密码
 */
export async function checkThirdPartyAuth(params: ThirdPartyAuthParams): Promise<ThirdPartyAuthResponse> {
  // 使用主要的requestClient，与登录接口保持一致
  return requestClient.get<ThirdPartyAuthResponse>('/system/openApi/user/checkAuth', {
    params,
  });
}

// ==================== 账号配置API ====================

/**
 * 更新三方平台账号密码
 */
export async function updatePlatformUserInfo(data: UpdatePlatformUserInfoParams): Promise<UpdatePlatformUserInfoResponse> {
  return apiCall(() => httpClient.post<ApiResponse<UpdatePlatformUserInfoResponse>>('/users/update_platform_user_info', data));
}

// ==================== AI科目相关API ====================

/**
 * 查询AI科目列表
 */
export async function fetchAIAccountSubjectList(params: { company_name: string }): Promise<AIAccountSubjectResponseData> {
  return apiCall(() => httpClient.get<ApiResponse<AIAccountSubjectResponseData>>('/account-subjects/list_ai', { params }));
}

/**
 * 修改科目映射
 */
export async function updateSubjectMapping(data: SubjectMappingUpdateParams): Promise<SubjectMappingUpdateResponse> {
  return apiCall(() => httpClient.post<ApiResponse<SubjectMappingUpdateResponse>>('/ai-accounting-config/subject-mapping/update', data));
}

/**
 * 同步科目辅助核算
 */
export async function syncAccountSubjects(data: SyncAccountSubjectsParams): Promise<SyncAccountSubjectsResponse> {
  return apiCall(() => httpClient.post<ApiResponse<SyncAccountSubjectsResponse>>('/account-subjects/sync', data));
}

// ==================== 群发消息相关API ====================

/**
 * 获取群列表
 * 说明：通过此接口可以获取所有当前登录精算家平台的授权企业的企业微信所有外部群列表
 */
export async function fetchChatGroupList(): Promise<ChatGroupInfo[]> {
  try {
    // 直接调用外部API，不使用内部的httpClient
    const response = await fetch('https://bot.jsj-ai.com/demo/listAllChatGroup', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: ChatGroupListResponse = await response.json();

    if (result.success && result.code === 200) {
      return result.data || [];
    }

    throw new Error(result.message || '获取群列表失败');
  } catch (error: any) {
    console.error('获取群列表失败:', error);
    throw error;
  }
}

/**
 * 群发消息
 * 描述：上个接口返回的群聊列表，在前端可以勾选，勾选之后封装成一个chatIdList，然后和发送的消息文本一起组装成一个json请求后端
 */
export async function sendGroupMessage(data: SendGroupMessageParams): Promise<string> {
  try {
    // 直接调用外部API，不使用内部的httpClient
    const response = await fetch('https://bot.jsj-ai.com/demo/sendMsg', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: SendGroupMessageResponse = await response.json();

    if (result.success && result.code === 200) {
      return result.data || '发送成功';
    }

    throw new Error(result.message || '发送消息失败');
  } catch (error: any) {
    console.error('发送群消息失败:', error);
    throw error;
  }
}
